{"name": "@fishivo/mobile", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "react-native start --port 8090 --client-logs", "start": "react-native start --port 8090 --client-logs", "build": "echo 'Mobile app build completed - no bundling needed for development'", "android": "react-native run-android", "ios": "react-native run-ios", "clean": "cd android && ./gradlew clean && cd .. && rm -rf node_modules && yarn install", "reset": "npx react-native-clean-project", "pods": "cd ios && pod install && cd ..", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace Fishivo.xcworkspace -scheme Fishivo -configuration Release -destination generic/platform=iOS -archivePath Fishivo.xcarchive archive", "lint": "eslint .", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@fishivo/config": "workspace:*", "@fishivo/hooks": "workspace:*", "@fishivo/services": "workspace:*", "@fishivo/shared": "workspace:*", "@fishivo/ui": "workspace:*", "@fishivo/utils": "workspace:*", "@react-native-async-storage/async-storage": "1.19.3", "@react-native-community/geolocation": "3.0.6", "@react-native-google-signin/google-signin": "10.0.1", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@rnmapbox/maps": "10.1.39", "@supabase/supabase-js": "^2.50.2", "axios": "1.6.2", "dotenv": "16.3.1", "lucide-react-native": "0.523.0", "react": "18.2.0", "react-native": "0.74.4", "react-native-config": "1.5.1", "react-native-curved-bottom-bar": "^3.5.1", "react-native-gesture-handler": "^2.16.2", "react-native-haptic-feedback": "2.2.0", "react-native-image-picker": "7.0.2", "react-native-linear-gradient": "2.8.3", "react-native-permissions": "3.8.4", "react-native-reanimated": "3.16.7", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "^3.31.1", "react-native-svg": "15.12.0", "react-native-vector-icons": "10.0.2"}, "devDependencies": {"@babel/core": "7.22.20", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-arrow-functions": "^7.22.5", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.8", "@babel/plugin-transform-shorthand-properties": "^7.22.5", "@babel/plugin-transform-template-literals": "^7.22.5", "@babel/preset-env": "7.22.20", "@babel/runtime": "7.22.15", "@react-native-community/cli": "11.3.7", "@react-native/babel-preset": "0.74.0", "@react-native/eslint-config": "0.74.0", "@react-native/metro-config": "0.74.0", "@react-native/typescript-config": "0.74.0", "@types/react": "18.2.21", "@types/react-native": "^0.73.0", "@types/react-native-vector-icons": "6.4.14", "@typescript-eslint/eslint-plugin": "6.7.0", "@typescript-eslint/parser": "6.7.0", "babel-jest": "29.7.0", "babel-plugin-module-resolver": "5.0.0", "eslint": "8.49.0", "jest": "29.7.0", "metro-react-native-babel-preset": "0.76.8", "patch-package": "7.0.0", "prettier": "3.0.3", "react-native-clean-project": "4.0.1", "react-test-renderer": "18.2.0", "typescript": "5.2.2"}, "private": true, "resolutions": {"react": "18.2.0", "@types/react": "18.2.21", "react-native": "0.74.4"}, "keywords": ["fishivo", "fishing", "social", "platform", "react-native", "mobile", "ios", "android"]}